<template>
  <div class="character-generation-container">
    <!-- 项目创意组件 -->
    <div class="project-idea-section">
      <ProjectIdea ref="projectIdeaRef" :voices="voices" :isLoadingVoices="isLoadingVoices"
        :conversation-id="conversationId" @update-ratio="updateOutputRatio" @open-voice-selector="handleOpenVoiceSelector" />
    </div>

    <!-- 角色生成内容 -->
    <div class="result-content" v-if="editResult">
      <AXThoughtChain v-if="editResult.status === 'in-progress'" :loading="true" :thinking="true">
        <div class="thought-content">正在生成角色，请稍候...</div>
      </AXThoughtChain>
    <div class="role-design-section">
      <div class="role-cards">
        <div v-for="role in characters" :key="role.charID" class="role-card">
          <div class="role-header">
            <div class="role-basic">
              <div class="role-info">
                <h4>{{ role.name }}</h4>
                <el-tag size="small" type="success" effect="plain" @click="insertTextAtCursor('角色ID:'+role.charID)">ID: {{ role.charID }}</el-tag>
              </div>
            </div>
            <div class="role-actions">
              <!-- <el-button type="primary" size="small" circle @click="openCharacterEditor(role.charID)">
                <el-icon>
                  <Edit />
                </el-icon>
              </el-button> -->
              <el-button type="danger" size="small" circle @click="insertTextSendMessage('调用工具删除角色【' + role.charID +'】')">
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </div>
          </div>
          <div class="role-content-wrapper">
            <div class="character-image-container">
              <ImagePreview 
                type="character"
                :imageUrl="role.image"
                :title="{title: role.name, description: role.description}"
                :itemId="role.charID" 
                :state="role.imageStatus"
                :conversationId="conversationId"
                @error="handleImageError"
              />
              
              <!-- 本地上传按钮 -->
              <!-- <div class="upload-image-button" @click="openUploadDialog(role.charID)" v-if="!role.image || role.imageError">
                <el-icon><Upload /></el-icon>
                <span>本地上传</span>
              </div> -->
            </div>
            
            <div class="role-content">
              <div class="role-section" v-if="false">
                <div class="section-title">
                  <el-icon>
                    <InfoFilled />
                  </el-icon>
                  <span>角色描述</span>
                  <div class="field-actions">
                    <template v-if="!editingState[`${role.charID}_description`]">
                      <el-tooltip content="编辑" placement="top" :effect="'light'">
                        <div class="edit-btn" @click.stop="startEditing(role.charID, 'description')">
                          <el-icon><Edit /></el-icon>
                        </div>
                      </el-tooltip>
                    </template>
                    <template v-else>
                      <el-tooltip content="取消" placement="top" :effect="'light'">
                        <div class="cancel-btn" @click.stop="cancelEditing(role.charID, 'description')">
                          <el-icon><Close /></el-icon>
                        </div>
                      </el-tooltip>
                      <el-tooltip content="保存" placement="top" :effect="'light'">
                        <div class="save-btn" @click.stop="saveEditing(role.charID, 'description')">
                          <el-icon><CircleCheck /></el-icon>
                        </div>
                      </el-tooltip>
                    </template>
                  </div>
                </div>
                <div class="role-description" v-if="!editingState[`${role.charID}_description`]">
                  {{ role.description || '暂无描述' }}
                </div>
                <el-input 
                  v-else 
                  v-model="getEditingCharacter(role.charID).description" 
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 12 }"
                  placeholder="请输入角色描述"
                  class="edit-textarea"
                ></el-input>
              </div>
              
              <!-- 角色背景部分 -->
              <div class="role-section">
                <div class="section-title">
                  <el-icon>
                    <Document />
                  </el-icon>
                  <span>角色背景</span>
                  <div class="field-actions">
                    <template v-if="!editingState[`${role.charID}_background`]">
                      <el-tooltip content="编辑" placement="top" :effect="'light'">
                        <div class="edit-btn" @click.stop="startEditing(role.charID, 'background')">
                          <el-icon><Edit /></el-icon>
                        </div>
                      </el-tooltip>
                    </template>
                    <template v-else>
                      <el-tooltip content="取消" placement="top" :effect="'light'">
                        <div class="cancel-btn" @click.stop="cancelEditing(role.charID, 'background')">
                          <el-icon><Close /></el-icon>
                        </div>
                      </el-tooltip>
                      <el-tooltip content="保存" placement="top" :effect="'light'">
                        <div class="save-btn" @click.stop="saveEditing(role.charID, 'background')">
                          <el-icon><CircleCheck /></el-icon>
                        </div>
                      </el-tooltip>
                    </template>
                  </div>
                </div>
                <div class="role-description" v-if="!editingState[`${role.charID}_background`]">
                  {{ role.background || '暂无背景信息' }}
                </div>
                <el-input 
                  v-else 
                  v-model="getEditingCharacter(role.charID).background" 
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 12 }"
                  placeholder="请输入角色背景"
                  class="edit-textarea"
                ></el-input>
              </div>
              
              <!-- 角色音色部分 -->
              <div class="role-section">
                <div class="section-title">
                  <el-icon>
                    <Microphone />
                  </el-icon>
                  <span>角色音色</span>
                </div>
                <div class="role-description voice-display" v-if="!editingState[`${role.charID}_voice_id`]">
                  <span class="voice-name" @click.stop="openVoiceSelector(role.charID, role.voice_id)">{{ getVoiceName(role.voice_id) }}</span>
                  <span class="voice-id-note" v-if="role.voice_id">(ID: {{ role.voice_id }})</span>
                  <!-- 添加试听按钮 -->
                  <div v-if="role.voice_id" class="voice-play-button" 
                    :class="{ 'playing': isPlaying(role.charID, role.voice_id) }" 
                    @click.stop="playVoice(role.charID, role.voice_id)">
                    <el-icon>
                      <VideoPause v-if="isPlaying(role.charID, role.voice_id)" />
                      <VideoPlay v-else />
                    </el-icon>
                    <span class="wave-animation" v-if="isPlaying(role.charID, role.voice_id)">
                      <span class="wave-bar"></span>
                      <span class="wave-bar"></span>
                      <span class="wave-bar"></span>
                      <span class="wave-bar"></span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片上传对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传角色图片"
      width="500px"
    >
      <el-upload
        class="image-uploader"
        action="#"
        :auto-upload="false"
        :show-file-list="true"
        :on-change="handleFileChange"
        accept="image/*"
      >
        <div class="upload-trigger">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <div class="upload-text">点击或拖拽图片上传</div>
        </div>
      </el-upload>
      
      <div class="upload-preview" v-if="uploadedImageUrl">
        <img :src="uploadedImageUrl" alt="上传预览" />
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmUpload" 
            :loading="uploadLoading"
            :disabled="!uploadedImageUrl"
          >
            确认上传
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 音色选择器对话框 -->
    <el-dialog
      v-model="voiceSelectorDialogVisible"
      title="选择角色音色"
      width="1200px"
      destroy-on-close
      class="voice-selector-dialog"
    >
      <VoiceSelector
        :voices="props.voices"
        :selectedVoice="selectedVoiceId ? Number(selectedVoiceId) : null"
        @update:selectedVoice="(voiceId) => selectedVoiceId = voiceId"
        :isLoading="props.isLoadingVoices"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="voiceSelectorDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmVoiceSelection" 
          >
            确认选择
          </el-button>
        </div>
      </template>
    </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ThoughtChain as AXThoughtChain } from 'ant-design-x-vue'
import { Edit, Delete, InfoFilled, Upload, Plus, Close, CircleCheck, Document, Microphone, VideoPause, VideoPlay } from '@element-plus/icons-vue'
import { ref, onMounted, watch, computed, nextTick, onBeforeUnmount, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ImagePreview from './ImagePreview.vue'
import { uploadToOSS } from '@/api/oss.js'
import { getAudioCount, updateCharacterIdAudio, updateConversation } from '@/api/auth.js'
import VoiceSelector from '../selector/VoiceSelector.vue'
import ProjectIdea from '../ProjectIdea.vue'

const props = defineProps({
  editResult: {
    type: Object,
    default: null
  },
  shotRoles: {
    type: Object,
    default: () => ({ characters: [] })
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  conversationId: {
    type: String,
    default: ''
  },
  voices: {
    type: Array,
    default: () => []
  },
  isLoadingVoices: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['add-character', 'delete-character', 'update-character', 'update:isEditing', 'update:shotRoles', 'update-ratio']);

// 创建ProjectIdea组件的引用
const projectIdeaRef = ref(null)

// 创建一个本地状态来跟踪图片加载错误
const characters = computed(() => {
  if (!props.shotRoles || !props.shotRoles.characters) return []
  console.log('CharacterGeneration - computed characters 被重新计算:', props.shotRoles?.characters?.length)
  return props.shotRoles.characters.map(role => ({
    ...role,
    imageError: role.imageError || false
  }))
})

// // 添加监听shotRoles的变化的watch函数
// watch(() => props.shotRoles, (newVal, oldVal) => {
//   console.log('CharacterGeneration - shotRoles 发生变化:', 
//     '新值角色数量:', newVal?.characters?.length,
//     '旧值角色数量:', oldVal?.characters?.length)
  
//   // 如果需要，可以在这里添加额外的逻辑来处理shotRoles变化
// }, { deep: true })

// 上传相关状态
const uploadDialogVisible = ref(false);
const currentCharacterId = ref(null);
const uploadedFile = ref(null);
const uploadedImageUrl = ref('');
const uploadLoading = ref(false);

// 音色选择器相关状态
const voiceSelectorDialogVisible = ref(false);
const currentEditingCharId = ref(null);
const selectedVoiceId = ref(null);

// 编辑状态管理
const editingState = reactive({});

// 临时角色数据，用于编辑时的临时存储
const tempCharacters = ref([]);

// 监听角色数据变化，更新临时数据
watch(() => props.shotRoles, (newVal) => {
  if (newVal && newVal.characters) {
    // 深拷贝确保不直接修改原始数据
    tempCharacters.value = JSON.parse(JSON.stringify(newVal.characters));
  }
}, { immediate: true, deep: true });

// 获取正在编辑的角色数据
const getEditingCharacter = (charID) => {
  let character = tempCharacters.value.find(c => c.charID === charID);
  if (!character) {
    // 如果找不到角色，可能是因为角色数据尚未加载，创建一个临时对象
    character = {
      charID: charID,
      name: '',
      description: '',
      background: '',
      voice: ''
    };
    tempCharacters.value.push(character);
  }
  return character;
};

// 开始编辑
const startEditing = (charID, field) => {
  const key = `${charID}_${field}`;
  // 确保tempCharacters已初始化
  if (tempCharacters.value.length === 0 && props.shotRoles.characters.length > 0) {
    tempCharacters.value = JSON.parse(JSON.stringify(props.shotRoles.characters));
  }
  // 设置编辑状态
  editingState[key] = true;
};

// 取消编辑
const cancelEditing = (charID, field) => {
  const key = `${charID}_${field}`;
  // 重置编辑状态
  editingState[key] = false;
  // 重置临时数据
  if (props.shotRoles.characters.length > 0) {
    tempCharacters.value = JSON.parse(JSON.stringify(props.shotRoles.characters));
  }
};

// 保存编辑
const saveEditing = (charID, field) => {
  const key = `${charID}_${field}`;
  // 重置编辑状态
  editingState[key] = false;
  
  // 更新原始数据
  const updatedCharacters = [...props.shotRoles.characters];
  const characterIndex = updatedCharacters.findIndex(c => c.charID === charID);
  
  if (characterIndex !== -1) {
    const editingCharacter = tempCharacters.value.find(c => c.charID === charID);
    
    if (editingCharacter) {
      // 只更新正在编辑的字段
      updatedCharacters[characterIndex][field] = editingCharacter[field];
      
      // 创建更新后的shotRoles对象
      const updatedShotRoles = {
        ...props.shotRoles,
        characters: updatedCharacters
      };
      
      // 通知父组件更新（两种方式都使用，以兼容不同的集成方式）
      emit('update-character', updatedShotRoles);
      emit('update:shotRoles', updatedShotRoles);
      // ElMessage.success('保存成功');
    }
  }
};

// 处理图片加载失败
const handleImageError = (charID) => {
  console.log('处理图片加载失败:', charID)
  if (!props.shotRoles || !props.shotRoles.characters) return
  
  // 在计算属性中找到并标记为加载失败
  characters.value.forEach(role => {
    if (role.charID === charID) {
      role.imageError = true
    }
  })
  
  // 同时在原始数据中标记，确保切换回来后状态保持
  props.shotRoles.characters.forEach(role => {
    if (role.charID === charID) {
      role.imageError = true
    }
  })
}

// 处理AI修图更新事件
const handleAIImageUpdate = (event) => {
  if (!event.detail) return;
  
  const { originalUrl, newUrl } = event.detail;
  console.log('收到AI修图更新事件:', originalUrl, '->', newUrl);
  
  // 更新角色图片
  if (characters.value && characters.value.length > 0) {
    characters.value.forEach(role => {
      if (role.image === originalUrl) {
        console.log('更新角色图片:', role.name, role.charID);
        role.image = newUrl;
        role.imageError = false;
      }
    });
  }
  
  // 同时更新原始数据
  if (props.shotRoles && props.shotRoles.characters) {
    props.shotRoles.characters.forEach(role => {
      if (role.image === originalUrl) {
        role.image = newUrl;
        role.imageError = false;
      }
    });
  }
}

// 打开上传对话框
const openUploadDialog = (charID) => {
  currentCharacterId.value = charID;
  uploadDialogVisible.value = true;
  // 重置上传状态
  uploadedFile.value = null;
  uploadedImageUrl.value = '';
};

// 处理文件变更
const handleFileChange = (file) => {
  const isImage = file.raw.type.indexOf('image/') !== -1;
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }

  uploadedFile.value = file.raw;
  uploadedImageUrl.value = URL.createObjectURL(file.raw);
  return false;
};

// 确认上传
const confirmUpload = async () => {
  if (!uploadedFile.value || !currentCharacterId.value) {
    ElMessage.warning('请选择图片');
    return;
  }

  uploadLoading.value = true;
  try {
    // 上传图片到OSS
    const result = await uploadToOSS(uploadedFile.value, 'character-images');
    if (result && result.url) {
      // 更新角色图片
      updateCharacterImage(currentCharacterId.value, result.url);
      ElMessage.success('图片上传成功');
      uploadDialogVisible.value = false;
    } else {
      throw new Error('上传失败');
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    ElMessage.error('图片上传失败，请重试');
  } finally {
    uploadLoading.value = false;
  }
};

// 更新角色图片
const updateCharacterImage = (charID, imageUrl) => {
  // 更新本地计算属性中的图片
  characters.value.forEach(role => {
    if (role.charID === charID) {
      role.image = imageUrl;
      role.imageError = false;
    }
  });
  
  // 更新原始数据
  props.shotRoles.characters.forEach(role => {
    if (role.charID === charID) {
      role.image = imageUrl;
      role.imageError = false;
    }
  });

  // 这里可以调用API来更新角色图片
  // 省略API调用实现...
};

// 向输入框插入文本的方法
const insertTextAtCursor = (text) => {
  window.insertTextToChatInput(text);
}

// 在组件挂载时添加事件监听
onMounted(() => {
  // 监听AI修图完成事件
  window.addEventListener('ai-image-updated', handleAIImageUpdate);
});

// 在组件卸载前移除事件监听
onBeforeUnmount(() => {
  // 移除AI修图事件监听
  window.removeEventListener('ai-image-updated', handleAIImageUpdate);
  
  // 停止正在播放的音频
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value = null;
    currentPlayingId.value = { charId: null, voiceId: null };
  }
});

const insertTextSendMessage = (text) => {
  if (window.insertTextSendMessage && typeof window.insertTextSendMessage === 'function') {
    window.insertTextSendMessage(text);
  }
}

// 打开角色编辑器
const openCharacterEditor = (charID) => {
  // 直接进入所有编辑模式
  startEditing(charID, 'description');
  startEditing(charID, 'background');
  startEditing(charID, 'voice_id');
};

// 当前播放的音频元素和ID
const currentAudio = ref(null);
const currentPlayingId = ref({ charId: null, voiceId: null });

// 判断是否正在播放特定音色
const isPlaying = (charId, voiceId) => {
  return currentPlayingId.value.charId === charId && 
         currentPlayingId.value.voiceId === voiceId && 
         currentAudio.value && 
         !currentAudio.value.paused;
};

// 播放音色示例
const playVoice = (charId, voiceId) => {
  // 如果正在播放当前音色，则暂停
  if (isPlaying(charId, voiceId)) {
    currentAudio.value.pause();
    currentAudio.value = null;
    currentPlayingId.value = { charId: null, voiceId: null };
    return;
  }
  
  // 如果有其他正在播放的音频，先停止
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value = null;
    currentPlayingId.value = { charId: null, voiceId: null };
  }
  
  // 查找选中的音色
  const voice = props.voices.find(v => v.id == voiceId);
  
  // 创建新的音频元素并播放
  if (voice && voice.audioUrl) {
    const audioUrl = voice.audioUrl;
    const audio = new Audio(audioUrl);
    
    // 设置事件监听
    audio.addEventListener('play', () => {
      currentPlayingId.value = { charId, voiceId };
    });
    
    audio.addEventListener('ended', () => {
      currentAudio.value = null;
      currentPlayingId.value = { charId: null, voiceId: null };
    });
    
    audio.addEventListener('pause', () => {
      if (currentPlayingId.value.charId === charId && currentPlayingId.value.voiceId === voiceId) {
        currentPlayingId.value = { charId: null, voiceId: null };
      }
    });
    
    audio.addEventListener('error', () => {
      console.error('音频播放错误');
      currentAudio.value = null;
      currentPlayingId.value = { charId: null, voiceId: null };
      ElMessage.error('音频播放失败，请重试');
    });
    
    // 播放
    audio.play().catch(error => {
      console.error('播放失败:', error);
      currentAudio.value = null;
      currentPlayingId.value = { charId: null, voiceId: null };
      ElMessage.error('音频播放失败，请重试');
    });
    
    currentAudio.value = audio;
  } else {
    ElMessage.warning('该音色没有试听音频');
  }
};

// 获取语音名称
const getVoiceName = (voiceId) => {
  if (!voiceId) return '未选择';
  const voice = props.voices.find(v => v.id == voiceId);
  return voice ? voice.name : voiceId;
};

// 打开音色选择器对话框
const openVoiceSelector = (charID, currentVoiceId) => {
  currentEditingCharId.value = charID;
  selectedVoiceId.value = currentVoiceId || null;
  voiceSelectorDialogVisible.value = true;
};

// 处理来自ProjectIdea组件的音色选择器打开请求
const handleOpenVoiceSelector = (charID, currentVoiceId) => {
  openVoiceSelector(charID, currentVoiceId);
};

// 确认音色选择
const confirmVoiceSelection = async () => {
  if (!currentEditingCharId.value) return;

  try {
    // 检查是否是旁白音色更新
    if (currentEditingCharId.value === 'narrator') {
      // 处理旁白音色更新
      await handleNarratorVoiceUpdate();
    } else {
      // 处理角色音色更新
      await handleCharacterVoiceUpdate();
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return;
    }
    console.error('确认音色选择失败:', error);
    ElMessage.error('操作失败，请稍后再试');
  }
};

// 处理旁白音色更新
const handleNarratorVoiceUpdate = async () => {
  // 获取音频数量信息
  const audioCountResponse = await getAudioCount(props.conversationId);

  if (audioCountResponse.success) {
    const audioData = audioCountResponse.data;

    // 计算涉及的章节数量和旁白音频数量
    const totalChapters = audioData.chapters.length;
    const totalNarrationAudios = audioData.totalNarrationCount;

    // 获取选中音色名称
    const selectedVoice = props.voices.find(v => v.id == selectedVoiceId.value);
    const voiceName = selectedVoice ? selectedVoice.name : '未知音色';

    // 构建详细的确认信息
    const confirmMessage = `
      <div style="text-align: left; line-height: 1.6;">
        <p style="margin: 0 0 12px 0; font-size: 16px; font-weight: 600; color: #303133;">
          即将修改旁白音色
        </p>
        <div style="background: #f5f7fa; padding: 12px; border-radius: 6px; margin: 12px 0;">
          <p style="margin: 0 0 8px 0;"><strong>类型：</strong>旁白音色</p>
          <p style="margin: 0 0 8px 0;"><strong>新音色：</strong>${voiceName}</p>
          <p style="margin: 0;"><strong>音色ID：</strong>${selectedVoiceId.value}</p>
        </div>
        <div style="background: #fff2e8; padding: 12px; border-radius: 6px; border-left: 4px solid #e6a23c;">
          <p style="margin: 0 0 8px 0; color: #e6a23c; font-weight: 600;">影响范围：</p>
          <p style="margin: 0 0 4px 0;">• 涉及章节数量：<strong>${totalChapters}</strong> 个</p>
          <p style="margin: 0;">• 涉及旁白音频数量：<strong>${totalNarrationAudios}</strong> 个</p>
        </div>
        <p style="margin: 12px 0 0 0; font-size: 14px; color: #909399;">
          此操作将更新所有章节中的旁白音频文件，请确认是否继续？
        </p>
      </div>
    `;

    // 显示自定义确认弹框
    const confirmResult = await ElMessageBox.confirm(
      confirmMessage,
      '确认旁白音色修改',
      {
        confirmButtonText: '确认修改',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        customClass: 'character-voice-confirm-dialog'
      }
    );

    if (confirmResult === 'confirm') {

      // 调用更新会话API来更新旁白音色
      const updateData = {
        conversationId: props.conversationId,
        soundId: selectedVoiceId.value,
        soundName: voiceName
      };

      const updateResponse = await updateConversation(updateData);

      if (updateResponse.success) {
        // 通知ProjectIdea组件刷新数据
        if (projectIdeaRef.value) {
          projectIdeaRef.value.refreshProjectData();
        }

        ElMessage.success('旁白音色更新成功');
        voiceSelectorDialogVisible.value = false;
      } else {
        ElMessage.error(`旁白音色更新失败: ${updateResponse.errMessage}`);
      }
    }
  } else {
    ElMessage.error(`获取音频信息失败: ${audioCountResponse.errMessage}`);
  }
};

// 处理角色音色更新
const handleCharacterVoiceUpdate = async () => {
  // 获取音频数量信息
  const audioCountResponse = await getAudioCount(props.conversationId);

  if (audioCountResponse.success) {
    const audioData = audioCountResponse.data;

    // 计算涉及的章节数量
    const totalChapters = audioData.chapters.length;

    console.log('currentEditingCharId.value:', currentEditingCharId.value);

    // 从 chapters 中的 characterGroups 过滤出当前角色的音频数量
    let characterAudioCount = 0;
    if (audioData.chapters && Array.isArray(audioData.chapters)) {
      audioData.chapters.forEach(chapter => {
        if (chapter.characterGroups && Array.isArray(chapter.characterGroups)) {
          const currentCharacterGroup = chapter.characterGroups.find(
            group => group.characterId == currentEditingCharId.value
          );
          if (currentCharacterGroup && currentCharacterGroup.count) {
            characterAudioCount += currentCharacterGroup.count;
          }
        }
      });
    }

    // 获取当前角色名称
    const currentCharacter = props.shotRoles.characters.find(c => c.charID == currentEditingCharId.value);
    const characterName = currentCharacter ? currentCharacter.name : `角色ID: ${currentEditingCharId.value}`;

    // 获取选中音色名称
    const selectedVoice = props.voices.find(v => v.id == selectedVoiceId.value);
    const voiceName = selectedVoice ? selectedVoice.name : '未知音色';

    // 构建详细的确认信息
    const confirmMessage = `
      <div style="text-align: left; line-height: 1.6;">
        <p style="margin: 0 0 12px 0; font-size: 16px; font-weight: 600; color: #303133;">
          即将修改角色音色
        </p>
        <div style="background: #f5f7fa; padding: 12px; border-radius: 6px; margin: 12px 0;">
          <p style="margin: 0 0 8px 0;"><strong>角色：</strong>${characterName}</p>
          <p style="margin: 0 0 8px 0;"><strong>新音色：</strong>${voiceName}</p>
          <p style="margin: 0;"><strong>音色ID：</strong>${selectedVoiceId.value}</p>
        </div>
        <div style="background: #fff2e8; padding: 12px; border-radius: 6px; border-left: 4px solid #e6a23c;">
          <p style="margin: 0 0 8px 0; color: #e6a23c; font-weight: 600;">影响范围：</p>
          <p style="margin: 0 0 4px 0;">• 涉及章节数量：<strong>${totalChapters}</strong> 个</p>
          <p style="margin: 0;">• 涉及音频数量：<strong>${characterAudioCount}</strong> 个</p>
        </div>
        <p style="margin: 12px 0 0 0; font-size: 14px; color: #909399;">
          此操作将更新该角色在所有章节中的音频文件，请确认是否继续？
        </p>
      </div>
    `;

    // 显示自定义确认弹框
    const confirmResult = await ElMessageBox.confirm(
      confirmMessage,
      '确认角色音色修改',
      {
        confirmButtonText: '确认修改',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        customClass: 'character-voice-confirm-dialog'
      }
    );

    if (confirmResult === 'confirm') {
      // 用户确认后，调用更新音频API
      const updateParams = {
        soundId: selectedVoiceId.value,
        sessionId: props.conversationId,
        segmentIds: audioData.chapters.map(chapter => chapter.segmentId),
        characterId: currentEditingCharId.value
      };

      const updateResponse = await updateCharacterIdAudio(updateParams);

      if (updateResponse.success) {
        // 更新本地角色音色
        const updatedCharacters = [...props.shotRoles.characters];
        const characterIndex = updatedCharacters.findIndex(c => c.charID == currentEditingCharId.value);

        if (characterIndex !== -1) {
          updatedCharacters[characterIndex].voice_id = selectedVoiceId.value;

          const updatedShotRoles = {
            ...props.shotRoles,
            characters: updatedCharacters
          };

          emit('update-character', updatedShotRoles);
          emit('update:shotRoles', updatedShotRoles);
        }

        ElMessage.success('角色音色更新成功');
        voiceSelectorDialogVisible.value = false;
      } else {
        ElMessage.error(`角色音色更新失败: ${updateResponse.errMessage}`);
      }
    }
  } else {
    ElMessage.error(`获取音频信息失败: ${audioCountResponse.errMessage}`);
  }
};

// 更新输出比例的方法
const updateOutputRatio = (newRatio) => {
  emit('update-ratio', newRatio)
  console.log('CharacterGeneration - outputRatio 更新为:', newRatio)
}

</script>

<style scoped>
.character-generation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.project-idea-section {
  flex-shrink: 0;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark .project-idea-section {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

.result-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.role-design-section {
  padding: 6px;
  overflow: scroll;
  overflow-x: hidden;
  transition: background-color 0.3s;
}

.role-design-section::-webkit-scrollbar {
  width: 4px;
}

.role-design-section::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .role-design-section::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1e293b;
  transition: color 0.3s;
}

body.dark .section-header h3 {
  color: var(--text-primary);
}

.role-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 8px;
}

.role-card {
  border-left: 1px solid #6366f1;
  background-color: #ffffff79;
  border-radius: 12px;
  box-shadow: 0 2px 12px #6365f152;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

:deep(.el-tag) {
  cursor: pointer;
}


body.dark .role-card {
  background-color: #1e1e1e25;
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2); */
}

.role-card:hover {
  /* transform: scale(1.02); */
  /* box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15); */
  border: 2px solid #6365f1;
}

/* body.dark .role-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
} */

.role-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* background-color: #f8fafc; */
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark .role-header {
  /* background-color: var(--bg-tertiary); */
  /* border-color: var(--border-color); */
  border-bottom: 1px solid var(--border-light);
}

.role-basic {
  display: flex;
  align-items: center;
  gap: 12px;
}

.role-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 14px;
}

.role-info h4 {
  margin: 0;
  font-size: 18px;
  color: #1e293b;
  font-weight: 600;
  transition: color 0.3s;
}

body.dark .role-info h4 {
  color: var(--text-primary);
}

.role-actions {
  display: flex;
  gap: 8px;
}

.role-content-wrapper {
  display: flex;
  flex: 1;
  transition: flex-direction 0.3s ease;
}

.role-content {
  padding: 10px;
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 12px;
  overflow-y: auto;
  transition: all 0.3s ease;
}

body.dark .role-content {
  background-color: #1e1e1e25;
}

.role-section {
  display: flex;
  flex-direction: column;
  background-color: #f8fafc55;
  border-radius: 8px;
  padding: 4px 8px;
  transition: background-color 0.3s;
}

body.dark .role-section {
  background-color: #1e1e1e25;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 2px;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s;
}

body.dark .section-title {
  color: var(--text-tertiary);
}

.role-description {
  font-size: 15px;
  line-height: 1.8;
  color: #1e293b;
  text-align: left;
  white-space: pre-line;
  transition: color 0.3s;
  display: flex;
}

body.dark .role-description {
  color: var(--text-secondary);
}

@media (max-width: 800px) {
  .role-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .role-card {
    flex-direction: column;
  }

  :deep(.type-character) {
    width: 100%;
    height: 250px;
    border-radius: 0 0 8px 8px;
  }

  .role-content {
    padding: 6px 0 0 0;
  }

  .role-section {
    margin-bottom: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  body.dark .role-section {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
}

/* 思考链样式适配 */
:deep(.ax-thought-chain) {
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark :deep(.ax-thought-chain) {
  background-color: var(--bg-card);
  border-color: var(--border-color);
}

.thought-content {
  transition: color 0.3s;
}

body.dark .thought-content {
  color: var(--text-secondary);
}

/* 角色图片容器 */
.character-image-container {
  position: relative;
  width: 170px;
  flex-shrink: 0;
}

@media (max-width: 480px) {
  .role-content-wrapper {
    flex-direction: column;
  }

  .character-image-container{
    width: 100%;
  }
}

/* 上传按钮样式 */
.upload-image-button {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background-color: #3880ff;
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  z-index: 2;
}

.upload-image-button:hover {
  background-color: #3171e0;
  transform: scale(1.05);
}

/* 上传区域样式 */
.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 10px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  background-color: #fafafa;
  padding: 30px;
  text-align: center;
}

.image-uploader:hover {
  border-color: #6366f1;
  background-color: #f8f8ff;
}

body.dark .image-uploader {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .image-uploader:hover {
  border-color: #818cf8;
  background-color: var(--bg-quaternary, #2d3748);
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.upload-icon {
  font-size: 28px;
  color: #8c8c8c;
}

.upload-text {
  color: #8c8c8c;
  font-size: 14px;
}

body.dark .upload-icon,
body.dark .upload-text {
  color: var(--text-tertiary);
}

.upload-preview {
  margin-top: 20px;
  border-radius: 8px;
  overflow: hidden;
  max-height: 300px;
}

.upload-preview img {
  width: 100%;
  object-fit: contain;
}

.field-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.edit-btn, .cancel-btn, .save-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.edit-btn {
  background-color: #6366f1;
  color: white;
}

.edit-btn:hover {
  background-color: #4f46e5;
  transform: scale(1.1);
}

.cancel-btn {
  background-color: #f87171;
  color: white;
}

.cancel-btn:hover {
  background-color: #ef4444;
  transform: scale(1.1);
}

.save-btn {
  background-color: #10b981;
  color: white;
}

.save-btn:hover {
  background-color: #059669;
  transform: scale(1.1);
}

/* 编辑输入框样式 */
.edit-textarea {
  margin-top: 8px;
  width: 100%;
}

/* 添加音色相关样式 */
.voice-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.voice-id-note {
  font-size: 12px;
  color: #909399;
}

body.dark .voice-id-note {
  color: var(--text-tertiary);
}

.voice-play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(64, 160, 255, 0.5);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: auto;
  position: relative;
}

.voice-play-button:hover {
  transform: scale(1.1);
  background-color: #409eff;
}

.voice-play-button.playing {
  background-color: #67c23a;
}

.wave-animation {
  position: absolute;
  left: 50%;
  top: -14px;
  transform: translateX(-50%);
  display: flex;
  align-items: flex-end;
  height: 12px;
  width: 14px;
  gap: 2px;
}

.wave-bar {
  width: 2px;
  background-color: #67c23a;
  border-radius: 1px;
  animation: waveAnimation 0.8s infinite ease-in-out;
}

.wave-bar:nth-child(1) {
  height: 5px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 8px;
  animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
  height: 6px;
  animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
  height: 7px;
  animation-delay: 0.6s;
}

@keyframes waveAnimation {
  0%, 100% {
    transform: scaleY(0.6);
  }
  50% {
    transform: scaleY(1);
  }
}

.voice-selector-container {
  margin-top: 10px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fafafa;
  max-height: 300px;
  overflow: hidden;
  padding: 10px;
}

body.dark .voice-selector-container {
  border-color: var(--border-color);
  background-color: var(--bg-tertiary);
}

body.dark .add-voice-text {
  color: var(--primary-color);
}

/* 在StoryDesign的编辑模式中调整高度 */

/* 音色选择器对话框样式 */
.voice-selector-dialog {
  max-width: 90vw;
}

.voice-selector-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

/* 可点击的音色名称样式 */
.voice-name {
  cursor: pointer;
  color: #409eff;
  transition: all 0.3s;
  padding: 2px 8px;
  border-radius: 4px;
}

.voice-name:hover {
  background-color: rgba(64, 158, 255, 0.1);
  text-decoration: underline;
}

body.dark .voice-name {
  color: #6366f1;
}

body.dark .voice-name:hover {
  background-color: rgba(99, 102, 241, 0.1);
}

/* 自定义角色音色确认弹框样式 */
:deep(.character-voice-confirm-dialog) {
  .el-message-box {
    width: 480px;
    max-width: 90vw;
  }

  .el-message-box__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-message-box__title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }

  .el-message-box__content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .el-message-box__message {
    margin: 0;
    font-size: 14px;
    line-height: 1.6;
  }

  .el-message-box__btns {
    padding: 15px 20px 20px;
    border-top: 1px solid #ebeef5;
  }

  .el-button--primary {
    background-color: #e6a23c;
    border-color: #e6a23c;
  }

  .el-button--primary:hover {
    background-color: #eeb563;
    border-color: #eeb563;
  }
}

/* 暗色主题下的确认弹框样式 */
body.dark :deep(.character-voice-confirm-dialog) {
  .el-message-box {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
  }

  .el-message-box__header {
    border-bottom-color: var(--border-color);
  }

  .el-message-box__title {
    color: var(--text-primary);
  }

  .el-message-box__content {
    color: var(--text-primary);
  }

  .el-message-box__btns {
    border-top-color: var(--border-color);
  }
}
</style>