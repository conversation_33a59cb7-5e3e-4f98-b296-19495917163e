<template>
  <div v-if="visible" class="voice-confirm-overlay" @click="handleOverlayClick">
    <div class="voice-confirm-dialog" @click.stop>
      <!-- 头部图标 -->
      <div class="dialog-header">
        <div class="voice-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C13.1 2 14 2.9 14 4V12C14 13.1 13.1 14 12 14C10.9 14 10 13.1 10 12V4C10 2.9 10.9 2 12 2Z" 
                  fill="currentColor"/>
            <path d="M19 10V12C19 15.9 15.9 19 12 19C8.1 19 5 15.9 5 12V10H7V12C7 14.8 9.2 17 12 17C14.8 17 17 14.8 17 12V10H19Z" 
                  fill="currentColor"/>
            <path d="M12 19V22H8V20H16V22H12V19Z" fill="currentColor"/>
          </svg>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="dialog-content">
        <h3 class="dialog-title">{{ title }}</h3>
        <p class="dialog-message">{{ message }}</p>
        
        <!-- 音色信息 -->
        <div class="voice-info">
          <div v-if="characterName" class="info-item">
            <span class="info-label">角色：</span>
            <span class="info-value">{{ characterName }}</span>
          </div>
          <div v-if="voiceType && !characterName" class="info-item">
            <span class="info-label">类型：</span>
            <span class="info-value">{{ voiceType }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">新音色：</span>
            <span class="info-value">{{ voiceName }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">音色ID：</span>
            <span class="info-value">{{ voiceId }}</span>
          </div>
        </div>

        <!-- 影响范围 -->
        <div class="impact-info">
          <div class="impact-header">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                    fill="currentColor"/>
            </svg>
            <span>影响范围</span>
          </div>
          <div class="impact-details">
            <div class="impact-item">
              <span class="impact-label">涉及章节数量：</span>
              <span class="impact-value">{{ chapterCount }} 个</span>
            </div>
            <div class="impact-item">
              <span class="impact-label">涉及音频数量：</span>
              <span class="impact-value">{{ audioCount }} 个</span>
            </div>
          </div>
        </div>

        <p class="dialog-warning">
          {{ warningText }}
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="dialog-actions">
        <button 
          class="btn btn-secondary" 
          @click="handleCancel"
          :disabled="isProcessing"
        >
          取消
        </button>
        <button 
          class="btn btn-primary" 
          @click="handleConfirm"
          :disabled="isProcessing"
        >
          <span v-if="isProcessing" class="loading-spinner"></span>
          {{ isProcessing ? '处理中...' : '确认修改' }}
        </button>
      </div>

      <!-- 关闭按钮 -->
      <button 
        class="close-button" 
        @click="handleCancel"
        :disabled="isProcessing"
      >
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" 
                stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'VoiceConfirmDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '确认音色修改'
    },
    message: {
      type: String,
      default: '即将修改音色设置'
    },
    characterName: {
      type: String,
      default: ''
    },
    voiceType: {
      type: String,
      default: ''
    },
    voiceName: {
      type: String,
      default: ''
    },
    voiceId: {
      type: [String, Number],
      default: ''
    },
    chapterCount: {
      type: Number,
      default: 0
    },
    audioCount: {
      type: Number,
      default: 0
    },
    warningText: {
      type: String,
      default: '此操作将更新相关的音频文件，请确认是否继续？'
    },
    preventClose: {
      type: Boolean,
      default: false
    }
  },
  emits: ['confirm', 'cancel', 'close'],
  setup(props, { emit }) {
    const isProcessing = ref(false)

    // 处理确认
    const handleConfirm = () => {
      if (isProcessing.value) return
      isProcessing.value = true
      emit('confirm')
    }

    // 处理取消
    const handleCancel = () => {
      if (isProcessing.value || props.preventClose) return
      emit('cancel')
    }

    // 处理关闭
    const handleClose = () => {
      if (isProcessing.value || props.preventClose) return
      emit('close')
    }

    // 处理遮罩点击
    const handleOverlayClick = () => {
      if (!props.preventClose) {
        handleCancel()
      }
    }

    // 键盘事件处理
    const handleKeydown = (event) => {
      if (!props.visible) return
      
      switch (event.key) {
        case 'Escape':
          if (!props.preventClose) {
            handleCancel()
          }
          break
        case 'Enter':
          handleConfirm()
          break
      }
    }

    onMounted(() => {
      document.addEventListener('keydown', handleKeydown)
    })

    onUnmounted(() => {
      document.removeEventListener('keydown', handleKeydown)
    })

    return {
      isProcessing,
      handleConfirm,
      handleCancel,
      handleClose,
      handleOverlayClick
    }
  }
}
</script>

<style scoped>
.voice-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.voice-confirm-dialog {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 480px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  animation: slideUp 0.3s ease-out;
}

.dialog-header {
  padding: 24px 24px 0;
  text-align: center;
}

.voice-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto;
  background: linear-gradient(135deg, #e6a23c 0%, #f39c12 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.voice-icon svg {
  width: 24px;
  height: 24px;
}

.dialog-content {
  padding: 20px 24px;
  text-align: center;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px;
}

.dialog-message {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 20px;
}

.voice-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  text-align: left;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.info-value {
  font-size: 13px;
  font-weight: 600;
  color: #1a1a1a;
}

.impact-info {
  background: #fff2e8;
  border-radius: 8px;
  border-left: 4px solid #e6a23c;
  padding: 16px;
  margin-bottom: 16px;
  text-align: left;
}

.impact-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #e6a23c;
  font-weight: 600;
  font-size: 14px;
}

.impact-header svg {
  width: 16px;
  height: 16px;
}

.impact-details {
  margin-left: 24px;
}

.impact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.impact-item:last-child {
  margin-bottom: 0;
}

.impact-label {
  font-size: 13px;
  color: #b8860b;
}

.impact-value {
  font-size: 13px;
  font-weight: 600;
  color: #e6a23c;
}

.dialog-warning {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  margin: 0;
}

.dialog-actions {
  padding: 0 24px 24px;
  display: flex;
  gap: 12px;
}

.btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #e6a23c 0%, #f39c12 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4);
}

.btn-secondary {
  background: #f1f3f4;
  color: #5f6368;
}

.btn-secondary:hover:not(:disabled) {
  background: #e8eaed;
}

.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  border: none;
  background: #f1f3f4;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #5f6368;
  transition: all 0.2s ease;
}

.close-button:hover:not(:disabled) {
  background: #e8eaed;
  transform: scale(1.1);
}

.close-button svg {
  width: 16px;
  height: 16px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 深色主题支持 */
body.dark .voice-confirm-dialog {
  background: #2d2d2d;
  color: #e0e0e0;
}

body.dark .dialog-title {
  color: #ffffff;
}

body.dark .dialog-message {
  color: #b0b0b0;
}

body.dark .voice-info {
  background: #3a3a3a;
}

body.dark .info-label {
  color: #b0b0b0;
}

body.dark .info-value {
  color: #ffffff;
}

body.dark .impact-info {
  background: #3a2f1f;
  border-left-color: #e6a23c;
}

body.dark .impact-label {
  color: #d4a574;
}

body.dark .impact-value {
  color: #f39c12;
}

body.dark .dialog-warning {
  color: #b0b0b0;
}

body.dark .btn-secondary {
  background: #404040;
  color: #e0e0e0;
}

body.dark .btn-secondary:hover:not(:disabled) {
  background: #4a4a4a;
}

body.dark .close-button {
  background: #404040;
  color: #e0e0e0;
}

body.dark .close-button:hover:not(:disabled) {
  background: #4a4a4a;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .voice-confirm-dialog {
    margin: 16px;
    width: calc(100% - 32px);
  }

  .dialog-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .impact-details {
    margin-left: 0;
  }

  .info-item,
  .impact-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
